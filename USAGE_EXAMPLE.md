# Previous Task Optimize Endpoint Usage

## Overview
The `/api/previous_task_optimize` endpoint has been implemented following the existing codebase architecture. It provides optimized previous task data using the same request/response structure as the regular previous tasks endpoint.

## ✅ IMPLEMENTATION COMPLETE

The optimize endpoint is now **actively being used** in the `StoreHistoryPage`. The page has been updated to:

1. **Use the optimize endpoint by default** - The `fetchPreviousTasks` method now calls the optimize endpoint
2. **Maintain backward compatibility** - Added `fetchPreviousTasksRegular` method for the original endpoint
3. **Fixed property mappings** - Updated UI to use correct `PreviousTaskEntity` properties:
   - `cycleName` → `cycle`
   - `completedBy` → `storeName`
   - `dateScheduled` → `scheduledTimeStamp`
   - `taskComment` → `comment`
4. **Visual indicator** - App bar title shows "(Optimized)" to indicate the optimize endpoint is being used

## Usage Example

### 1. In a Cubit (similar to StoreHistoryCubit):

```dart
import 'package:storetrack_app/features/home/<USER>/usecases/get_previous_tasks_optimize_usecase.dart';

class OptimizedStoreHistoryCubit extends Cubit<StoreHistoryState> {
  final GetPreviousTasksOptimizeUseCase _getPreviousTasksOptimizeUseCase;

  OptimizedStoreHistoryCubit(this._getPreviousTasksOptimizeUseCase)
      : super(StoreHistoryInitial());

  Future<void> fetchOptimizedPreviousTasks(PreviousTasksRequestEntity request) async {
    emit(StoreHistoryLoading());

    try {
      final Result<PreviousTasksResponseEntity> result =
          await _getPreviousTasksOptimizeUseCase(request);

      if (result.isSuccess && result.data != null) {
        final previousTasks = result.data!.data?.previousTask ?? [];
        emit(StoreHistoryLoaded(previousTasks: previousTasks));
      } else {
        final errorMessage = result.error?.toString() ??
            'Unknown error occurred while fetching optimized previous tasks.';
        emit(StoreHistoryError(message: errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in OptimizedStoreHistoryCubit: $e");
      emit(StoreHistoryError(
          message: 'An unexpected error occurred. Please try again.'));
    }
  }
}
```

### 2. In a Page (similar to StoreHistoryPage):

```dart
// Get the use case from service locator
final optimizeUseCase = sl<GetPreviousTasksOptimizeUseCase>();

// Create request
final request = PreviousTasksRequestEntity(
  userId: int.tryParse(actualUserId) ?? 0,
  token: actualUserToken,
  deviceUid: actualDeviceUid,
  appversion: actualAppVersion,
  taskId: widget.taskId,
  specificTaskId: widget.storeId,
);

// Call the optimize endpoint
final result = await optimizeUseCase(request);

if (result.isSuccess && result.data != null) {
  final optimizedTasks = result.data!.data?.previousTask ?? [];
  // Use optimized tasks...
} else {
  // Handle error...
}
```

### 3. Request Structure:
The endpoint uses the same request structure as the regular previous tasks endpoint:

```json
{
  "user_id": 12345,
  "token": "user_auth_token",
  "device_uid": "device_unique_id",
  "appversion": "1.0.0",
  "task_id": 67890,
  "specific_task_id": 11111,
  "debug": 0
}
```

### 4. Response Structure:
The endpoint returns the same response structure as the regular previous tasks endpoint with optimized data.

## Endpoint Details

- **URL**: `/api/previous_task_optimize`
- **Method**: POST
- **Request**: `PreviousTasksRequestEntity`
- **Response**: `PreviousTasksResponseEntity`
- **Use Case**: `GetPreviousTasksOptimizeUseCase`

## Integration Notes

The new endpoint is fully integrated into the existing architecture:
- Registered in dependency injection (`service_locator.dart`)
- Follows the same error handling patterns
- Includes network connectivity checks
- Uses the existing entity structures
