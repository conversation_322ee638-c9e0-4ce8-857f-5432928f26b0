import 'dart:io';
import 'package:auto_route/auto_route.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/date_time_utils.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/widgets/empty_state.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/segment_indicator.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/simple_notification_card.dart';
import 'package:storetrack_app/features/notification/presentation/widgets/notification_card.dart';

import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../widgets/date_converter.dart';

@RoutePage()
class TaskDetailsPage extends StatefulWidget {
  final entities.TaskDetail task;

  const TaskDetailsPage({
    super.key,
    required this.task,
  });

  @override
  State<TaskDetailsPage> createState() => _TaskDetailsPageState();
}

class _TaskDetailsPageState extends State<TaskDetailsPage> {
  int completedFormAmount = 0;
  int maxFormAmount = 0;
  bool showAlert = false;
  bool showDocuments = false;

  // Track expanded documents
  Set<num?> expandedDocuments = {};

  // Track the currently viewed document file
  String? currentDocumentUrl;
  bool showWebView = false;
  WebViewController? webViewController;
  bool isWebViewLoading = true;

  // Track form display state
  bool showFormQuestions = false;
  entities.Document? currentFormDocument;

  @override
  void initState() {
    super.initState();

    // Calculate completed forms for progress indicator
    if (widget.task.forms != null) {
      for (var formModel in widget.task.forms!) {
        if (formModel.isVisionForm == true) {
          maxFormAmount++;

          if (formModel.isMandatory == true ||
              formModel.formCompleted == true) {
            completedFormAmount++;
          }
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: (showAlert) ? Colors.white : AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: 'Task Details',
        actions: [
          GestureDetector(
            child: Image.asset(
              AppAssets.alertIcon,
              scale: 4,
              color: showAlert ? AppColors.primaryBlue : AppColors.black,
            ),
            onTap: () {
              setState(() {
                showAlert = !showAlert;
                // If alerts are shown, hide documents
                if (showAlert) {
                  showDocuments = false;
                }
              });
            },
          ),
          const Gap(18),
          GestureDetector(
            child: Image.asset(
              AppAssets.documentsIcon,
              color: showDocuments ? AppColors.primaryBlue : AppColors.black,
              scale: 4,
            ),
            onTap: () {
              setState(() {
                showDocuments = !showDocuments;
                // If documents are shown, hide alerts
                if (showDocuments) {
                  showAlert = false;
                }
              });
            },
          ),
          const Gap(8),
          PopupMenuButton<String>(
  icon: Image.asset(
    AppAssets.taskMore,
    scale: 4,
  ),
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(12),
  ),
  elevation: 8,
  offset: const Offset(0, 20),
  color: Colors.white,
  position: PopupMenuPosition.under,
  constraints: const BoxConstraints(
    minWidth: 240,
    maxWidth: 320,
  ),
  itemBuilder: (context) => [
    _buildPopupMenuItem(context, 'Forms', AppAssets.taskForm),
    const PopupMenuDivider(height: .2,),
    _buildPopupMenuItem(context, 'POS', AppAssets.posIcon),
        const PopupMenuDivider(height: .2,),

    _buildPopupMenuItem(context, 'Notes', AppAssets.alertMessage),
        const PopupMenuDivider(height: .2,),

    _buildPopupMenuItem(context, 'Directions', AppAssets.appbarMap),
        const PopupMenuDivider(height: .2,),

    _buildPopupMenuItem(context, 'Store info', AppAssets.taskStore),
        const PopupMenuDivider(height: .2,),

    _buildPopupMenuItem(context, 'Store history', AppAssets.taskStoryHistory),
        const PopupMenuDivider(height: .2,),

    _buildPopupMenuItem(context, 'Task assistance', AppAssets.taskAssistant),
       const PopupMenuDivider(height: .2,),

    _buildPopupMenuItem(
      context,
      'Complete task',
      AppAssets.taskComplete,
      isBlue: true
    ),
  ],
  onSelected: (value) {
    // Handle menu item selection
    switch (value) {
      case 'Forms':
        // Handle forms action
        break;
      case 'POS':
        // Navigate to POS page with task data
        context.navigateTo(PosRoute(
          task: widget.task,
        ));
        break;
      case 'Notes':
        // Navigate to Notes page with task data
        context.navigateTo(NotesRoute(
          task: widget.task,
        ));
        break;
      case 'Directions':
        // Handle directions action
        break;
      case 'Store info':
        context.navigateTo(const StoreInfoRoute(
          // task: widget.task,
        ));
        // Handle store info action
        break;
      case 'Store history':
        // Navigate to Store history page with task data
        context.navigateTo(StoreHistoryRoute(
          storeId: (widget.task.storeId ?? 0).toInt(),
          taskId: (widget.task.taskId ?? 0).toInt(),
        ));
        break;
      case 'Task assistance':
        // Handle task assistance action
        break;
      case 'Complete task':
        // Handle complete task action
        break;
    }
  },
),
          // PopupMenuButton<String>(
          //   icon: Image.asset(
          //     AppAssets.taskMore,
          //     scale: 4,
          //   ),
          //   shape: RoundedRectangleBorder(
          //     borderRadius: BorderRadius.circular(12),
          //   ),
          //   elevation: 8,
          //   offset: const Offset(0, 20),
          //   color: Colors.white,
          //   position: PopupMenuPosition.under,
          //   constraints: const BoxConstraints(
          //     minWidth: 240,
          //     maxWidth: 320,
          //   ),
          //   itemBuilder: (context) => [
          //     _buildPopupMenuItem(context, 'Forms', AppAssets.taskForm),
          //     _buildPopupMenuItem(context, 'POS', AppAssets.posIcon),
          //     _buildPopupMenuItem(context, 'Notes', AppAssets.alertMessage),
          //     _buildPopupMenuItem(context, 'Directions', AppAssets.appbarMap),
          //     _buildPopupMenuItem(context, 'Store info', AppAssets.taskStore),
          //     _buildPopupMenuItem(
          //         context, 'Store history', AppAssets.taskStoryHistory),
          //     _buildPopupMenuItem(
          //         context, 'Task assistance', AppAssets.taskAssistant),
          //     _buildPopupMenuItem(
          //         context, 'Complete task', AppAssets.taskComplete,
          //         isBlue: true),
          //   ],
          //   onSelected: (value) {
          //     // Handle menu item selection
          //     switch (value) {
          //       case 'Forms':
          //         // Handle forms action
          //         break;
          //       case 'POS':
          //         // Navigate to POS page with task data
          //         context.navigateTo(PosRoute(
          //           task: widget.task,
          //         ));
          //         break;
          //       case 'Notes':
          //         // Navigate to Notes page with task data
          //         context.navigateTo(NotesRoute(
          //           task: widget.task,
          //         ));
          //         break;
          //       case 'Directions':
          //         // Handle directions action
          //         break;
          //       case 'Store info':
          //         context.navigateTo(const StoreInfoRoute(
          //             // task: widget.task,
          //             ));
          //         // Handle store info action
          //         break;
          //       case 'Store history':
          //         // Navigate to Store history page with task data
          //         context.navigateTo(StoreHistoryRoute(
          //           storeId: (widget.task.storeId ?? 0).toInt(),
          //           taskId: (widget.task.taskId ?? 0).toInt(),
          //         ));
          //         break;
          //       case 'Task assistance':
          //         // Handle task assistance action
          //         break;
          //       case 'Complete task':
          //         // Handle complete task action
          //         break;
          //     }
          //   },
          // ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Store information card
            _buildStoreInfoCard(context, widget.task),
            showAlert
                ? Container(color: Colors.white, child: const Divider())
                : const Gap(16),

            // Task alerts section
            showAlert ? _buildTaskAlertsSection(context) : Container(),
            showAlert ? const Gap(16) : Container(),

            // Task documents section
            showDocuments ? _buildTaskDocumentSection(context) : Container(),
            showDocuments ? const Gap(16) : Container(),

            // Progress cards row
            (showAlert || showDocuments)
                ? Container()
                : _buildProgressCards(
                    context, widget.task, maxFormAmount, completedFormAmount),
            (showAlert || showDocuments) ? Container() : const Gap(16),

            // Form queue and Forms tabs
            (showAlert || showDocuments)
                ? Container()
                : _buildFormTabs(context, widget.task),
            (showAlert || showDocuments) ? Container() : const Gap(16),

            // POS Received progress
            (showAlert || showDocuments)
                ? Container()
                : _buildPosReceivedProgress(context),
            (showAlert || showDocuments) ? Container() : const Gap(16),

            // Overview and Documents tabs
            (showAlert || showDocuments)
                ? Container()
                : _buildOverviewTabs(context),
            (showAlert || showDocuments) ? Container() : const Gap(16),

            // // Task notes
            // _buildTaskNotes(context),

            // const Gap(24),

            // // Action buttons
            // Padding(
            //   padding: const EdgeInsets.symmetric(horizontal: 16.0),
            //   child: _buildActionButtons(context),
            // ),
          ],
        ),
      ),
    );
  }

  Widget _buildStoreInfoCard(BuildContext context, entities.TaskDetail task) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Location icon and store name
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Icon(
                Icons.location_on_outlined,
                size: 16,
                color: AppColors.black,
              ),
              const Gap(8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      task.location ?? "",
                      style: textTheme.montserratTableSmall.copyWith(
                        overflow: TextOverflow.ellipsis,
                      ),
                      maxLines: 2,
                    ),
                    const Gap(4),
                    Text(
                      task.storeName ?? "",
                      style: textTheme.titleSmall?.copyWith(
                        overflow: TextOverflow.ellipsis,
                      ),
                      maxLines: 1,
                    ),
                    const Gap(4),
                    Text(
                      task.storeGroup ?? "",
                      style: textTheme.montserratTableSmall.copyWith(
                        overflow: TextOverflow.ellipsis,
                      ),
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
              // Client logo
              task.clientLogoUrl?.isEmpty == true
                  ? Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        // color: Colors.grey,
                        borderRadius: BorderRadius.circular(24),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: const Center(child: Text('N/A')),
                    )
                  : Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                            image: NetworkImage(task.clientLogoUrl ?? '')),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                    ),
            ],
          ),

          const Gap(8),
          const Divider(),
          const Gap(8),

          // Time and date
          Row(
            children: [
              const Icon(
                Icons.access_time,
                size: 16,
                color: AppColors.black,
              ),
              const Gap(8),
              Text(
                task.budget != null ? '${task.budget}m' : '',
                style: textTheme.montserratTableSmall
                    .copyWith(fontWeight: FontWeight.w500, fontSize: 12),
              ),
              const Gap(16),
              Image.asset(
                AppAssets.appbarCalendar,
                scale: 6,
                color: Colors.black,
              ),
              const Gap(8),
              Text(
                task.scheduledTimeStamp != null
                    ? convertToDateFormat(task.scheduledTimeStamp.toString())
                    : '',
                style: textTheme.bodySmall,
              ),
              const Spacer(),
              Text(
                'ID: ${task.taskId?.toString() ?? ''}',
                style: textTheme.bodySmall?.copyWith(
                  color: AppColors.black,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressCards(
      BuildContext context, entities.TaskDetail task, int max, int completed) {
    final textTheme = Theme.of(context).textTheme;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          // Task Completion card
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'Task Completion',
                    style: textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Gap(8),
                  Row(
                    children: [
                      Text(
                        (completed == 0 && max == 0)
                            ? '0%'
                            : '${((completed / max) * 100).toStringAsFixed(0)}%',
                        style: textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const Gap(8),
                  // Progress bar with segmented indicator
                  LayoutBuilder(
                    builder: (context, constraints) {
                      // Use the available width from constraints for the SegmentedProgressIndicator
                      final progressWidth = constraints.maxWidth;

                      // Center the line (line width is 1.5)
                      final adjustedPosition = (completed == 0 && max == 0)
                          ? 0.0
                          : (completed / max) - 0.75;

                      return Stack(
                        clipBehavior: Clip.none,
                        children: [
                          // Base progress indicator
                          SegmentedProgressIndicator(
                            progress: (completed == 0 && max == 0)
                                ? 0.0
                                : (completed / max), // 40% progress
                            totalWidth: progressWidth,
                            activeColor: AppColors.primaryBlue,
                            backgroundColor: Colors.grey.shade200,
                            dividerColor: Colors.black,
                            height: 10,
                            segments: 10,
                            borderRadius:
                                10, // Increased border radius for more curved edges
                          ),

                          // Vertical line
                          Positioned(
                            left:
                                adjustedPosition, // Using adjusted position to center the line
                            top:
                                -8, // Increased top position to make the line longer
                            bottom: 0, // Extend to the bottom of the stack
                            child: Container(
                              width: 1.5,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                  const Gap(8),
                  Text(
                    '${task.ctFormsCompletedCnt ?? 0} of ${task.ctFormsTotalCnt ?? 0} forms',
                    style: textTheme.bodySmall?.copyWith(
                      color: AppColors.black.withOpacity(0.6),
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const Gap(16),

          // POS Received card
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'POS Received',
                    style: textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  // const Gap(8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        AppAssets.dashboardPos,
                        width: 28,
                      ),
                      const Gap(8),
                      Text(
                        task.posReceived != null
                            ? task.posReceived.toString()
                            : '0',
                        style: textTheme.titleSmall?.copyWith(
                          fontSize: 40,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        ' of ${task.posItems != null ? task.posItems!.length : '0'}',
                        style: textTheme.bodySmall?.copyWith(
                          color: AppColors.black,
                        ),
                      ),
                    ],
                  ),
                  // const Gap(8),
                  Text(
                    'Delivered',
                    style: textTheme.bodySmall?.copyWith(
                      color: AppColors.primaryBlue,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormTabs(BuildContext context, entities.TaskDetail task) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      children: [
        // Tabs
        Padding(
          padding: const EdgeInsets.only(left: 16.0, right: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Form queue',
                style: textTheme.montserratTitleSmall,
                textAlign: TextAlign.center,
              ),
              const Spacer(),
              const Icon(
                Icons.check_box_outlined,
                size: 16,
              ),
              const Gap(4),
              Text('Forms', style: textTheme.montserratTitleExtraSmall),
              const Gap(16),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPosReceivedProgress(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    // Sample progress items - in a real app, these would come from your data model
    final List<Map<String, dynamic>> progressItems = [
      {'title': 'POS Received', 'progress': 0.0, 'text': '0 of 5'},
      {'title': 'Forms Completed', 'progress': 0.3, 'text': '3 of 10'},
      {'title': 'Photos Taken', 'progress': 0.5, 'text': '5 of 10'},
      {'title': 'Items Stickered', 'progress': 0.2, 'text': '4 of 20'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Horizontal scrollable list of progress items
        SizedBox(
          height: 100, // Fixed height for the scrollable container
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            itemCount: progressItems.length,
            itemBuilder: (context, index) {
              final item = progressItems[index];

              return Container(
                width: MediaQuery.of(context).size.width *
                    0.8, // Fixed width for each progress card
                margin:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item['title'],
                      style: textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const Gap(12),

                    // Progress bar
                    Row(
                      children: [
                        Expanded(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(4),
                            child: LinearProgressIndicator(
                              value: item['progress'],
                              backgroundColor: Colors.grey.shade200,
                              valueColor: const AlwaysStoppedAnimation<Color>(
                                  AppColors.primaryBlue),
                              minHeight: 8,
                            ),
                          ),
                        ),
                        const Gap(24),
                        Text(
                          item['text'],
                          style: textTheme.bodySmall?.copyWith(
                            color: AppColors.black.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),

                    const Gap(8),

                    // Progress text
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildOverviewTabs(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Overview',
                style: textTheme.montserratTitleSmall,
                textAlign: TextAlign.center,
              ),
              const Spacer(),
              Image.asset(
                AppAssets.documentsIcon,
                color: AppColors.black,
                scale: 5,
              ),
              const Gap(4),
              Text('Documents',
                  style: textTheme.montserratTitleExtraSmall.copyWith(
                    color: AppColors.black,
                  )),
              const Gap(8),
            ],
          ),
        ),
        // Overview content
        _buildOverviewContent(context),
      ],
    );
  }
  Widget _buildOverviewContent(BuildContext context) {
    // Group alerts by date to get the latest notification
    _groupTaskAlertsByDate();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Show the latest 1 notification if available
              if (widget.task.taskalerts != null && widget.task.taskalerts!.isNotEmpty) ...[
                // Get the latest notification (first one from the grouped alerts)
                Builder(
                  builder: (context) {
                    final latestEntry = groupedTaskAlerts.entries.first;
                    final latestAlert = latestEntry.value.first;

                    // Determine notification type based on content
                    SimpleNotificationType notificationType = SimpleNotificationType.manager;

                    if (latestAlert.subject?.toLowerCase().contains('urgent') == true ||
                        latestAlert.message?.toLowerCase().contains('urgent') == true ||
                        latestAlert.message?.toLowerCase().contains('asap') == true) {
                      notificationType = SimpleNotificationType.urgent;
                    }

                    return SimpleNotificationCard(
                      type: notificationType,
                      message: latestAlert.message ?? 'No message',
                    );
                  },
                ),
              ],

              // Static notification cards
               SimpleNotificationCard(
                type: SimpleNotificationType.manager,
                message: widget.task.taskNote!.isNotEmpty ? widget.task.taskNote! : 'No notes available',
              ),

              // Show a question and answer from forms if available
              Builder(
                builder: (context) {
                  // Check if forms and questions are available
                  if (widget.task.forms != null &&
                      widget.task.forms!.isNotEmpty &&
                      widget.task.forms![0].questions != null &&
                      widget.task.forms![0].questions!.isNotEmpty) {

                    final form = widget.task.forms![0];
                    final question = form.questions![0];

                    // Find the corresponding answer for this question
                    entities.QuestionAnswer? answer;
                    if (form.questionAnswers != null) {
                      try {
                        answer = form.questionAnswers!.firstWhere(
                          (qa) => qa.questionId == question.questionId,
                        );
                      } catch (e) {
                        // No answer found for this question
                        answer = null;
                      }
                    }

                    return SimpleNotificationCard(
                      type: SimpleNotificationType.info,
                      title: question.questionDescription ?? 'No question available',
                      message: _getAnswerText(answer),
                    );
                  }

                  // Fallback to form name and brief URL if no questions available
                  if (widget.task.forms != null && widget.task.forms!.isNotEmpty) {
                    return SimpleNotificationCard(
                      type: SimpleNotificationType.info,
                      title: widget.task.forms![0].formName ?? 'Form',
                      message: widget.task.forms![0].briefUrl ?? 'No brief available',
                    );
                  }

                  // Final fallback if no forms available
                  return const SimpleNotificationCard(
                    type: SimpleNotificationType.info,
                    title: 'No forms available',
                    message: 'No form data found for this task',
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }


  PopupMenuItem<String> _buildPopupMenuItem(
      BuildContext context, String title, String icon,
      {bool isBlue = false}) {
    return PopupMenuItem<String>(
      value: title,
      height: 48, // Set a consistent height for all menu items
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.montserratTitleSmall.copyWith(
                  color: isBlue ? AppColors.primaryBlue : Colors.black,
                ),
          ),
          const SizedBox(width: 16),
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              // border: Border.all(color: Colors.grey.shade400, width: 1.5),
            ),
            child: Center(
              child: Image.asset(
                icon,
                scale: 3,
                color: Colors.black,
                fit: BoxFit.cover,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Map to store alerts grouped by date
  Map<String, List<entities.Taskalert>> groupedTaskAlerts = {};

  // Set to track which forms are expanded
  Set<int> expandedForms = {};

  // Function to group alerts by date
  void _groupTaskAlertsByDate() {
    groupedTaskAlerts.clear();

    // For demonstration purposes, we'll create a fake date for each alert
    // In a real app, you would use the actual date from the alert
    for (var alert in widget.task.taskalerts ?? []) {
      // Create a fake date for demonstration (in a real app, use alert.date)
      // Spread alerts across the last 7 days for demonstration
      final index = widget.task.taskalerts!.indexOf(alert);
      final daysAgo = index % 3; // Spread across 3 days
      final fakeDate = DateTime.now().subtract(Duration(days: daysAgo));

      // Format the date string for display and grouping
      final formattedDate = DateFormat('EEE dd MMM').format(fakeDate);

      if (!groupedTaskAlerts.containsKey(formattedDate)) {
        groupedTaskAlerts[formattedDate] = [];
      }

      groupedTaskAlerts[formattedDate]!.add(alert);
    }
  }

  Widget _buildTaskAlertsSection(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    // Group alerts by date
    _groupTaskAlertsByDate();

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // List of task alerts or empty state
          // Always show notifications, even if empty
          widget.task.taskalerts == null
              ? const EmptyState(
                  message: 'No notifications available for this task')
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // List of task alerts grouped by date
                    ...groupedTaskAlerts.entries.map((entry) {
                      final date = entry.key;
                      final alertsForThisDate = entry.value;
                      final isToday = date ==
                          DateFormat('EEE dd MMM').format(DateTime.now());

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Date header
                          Padding(
                            padding: const EdgeInsets.only(
                              bottom: 10,
                            ),
                            child: Text(
                              date,
                              style: textTheme.titleSmall?.copyWith(
                                color: isToday
                                    ? AppColors.primaryBlue
                                    : AppColors.black,
                              ),
                            ),
                          ),

                          // Alerts for this date
                          ...alertsForThisDate.map((alert) {
                            // Determine notification type based on content
                            NotificationType notificationType =
                                NotificationType.message;

                            if (alert.subject
                                        ?.toLowerCase()
                                        .contains('urgent') ==
                                    true ||
                                alert.message
                                        ?.toLowerCase()
                                        .contains('urgent') ==
                                    true ||
                                alert.message?.toLowerCase().contains('asap') ==
                                    true) {
                              notificationType = NotificationType.urgent;
                            }

                            // Calculate a fake time ago for demonstration
                            final index = alertsForThisDate.indexOf(alert);
                            final hoursAgo = 1 +
                                (index * 2); // Spread across different hours
                            final fakeDate = DateTime.now()
                                .subtract(Duration(hours: hoursAgo));
                            final timeAgo = getTimeAgo(fakeDate);

                            return NotificationCard(
                              type: notificationType,
                              message: alert.message ?? 'No message',
                              company: widget.task.client ?? 'Unknown',
                              task: alert.subject ?? 'No subject',
                              location: widget.task.location ?? 'No location',
                              timeAgo: timeAgo,
                              duration: widget.task.budget != null
                                  ? '${widget.task.budget}m'
                                  : '0m',
                            );
                          }),

                          // Add divider between date groups
                          if (entry.key != groupedTaskAlerts.keys.last)
                            Padding(
                              padding:
                                  const EdgeInsets.only(top: 8.0, bottom: 16.0),
                              child: Divider(
                                height: 1,
                                thickness: 1,
                                color: AppColors.appBarBorderBlack,
                              ),
                            ),
                        ],
                      );
                    }),
                  ],
                ),
        ],
      ),
    );
  }

  var a = {
    "project_id": 0,
    "document_id": 100507,
    "document_type_id": 2,
    "document_name": "Brief",
    "document_icon_link":
        "https://field.storetrack.com.au/Images/icons/sMonthly-brief-icon-trans.gif",
    "files": [
      {
        "document_id": 100507,
        "project_id": 0,
        "document_file_link":
            "https://field.storetrack.com.au/WebsiteData/ProjectFiles/1003/uploads/28101962_Amazon.com.au__Order_503_8339207_9011806.pdf",
        "file_id": 127937,
        "modified_time_stamp_file": "2023-06-07T16:02:00"
      }
    ],
    "modified_time_stamp_document": "2023-06-07T16:02:00"
  };
  Widget _buildTaskDocumentSection(BuildContext context) {
    // If showing webview, display the webview instead of document list
    if (showWebView && currentDocumentUrl != null) {
      return _buildDocumentWebView(context);
    }

    // If showing form questions, display the form questions and answers
    if (showFormQuestions) {
      return _buildFormQuestionsView(context);
    }

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      // padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // List of documents or empty state
          widget.task.documents == null
              ? const Padding(
                  padding: EdgeInsets.all(0),
                  child: EmptyState(
                      message: 'No documents available for this task'),
                )
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Document categories
                    Padding(
                      padding:
                          const EdgeInsets.only(top: 16, left: 16, right: 16),
                      child: _buildDocumentCategory(true, context, 'Main',
                          [] // Always empty for main section as we'll show Brief statically
                          ),
                    ),

                    // const SizedBox(height: 8),
                     Divider(color: AppColors.black10,),
                    // const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.only(
                          bottom: 16, left: 16, right: 16, top: 16),

                      // padding: const EdgeInsets.all(8.0),
                      child: _buildDocumentCategory(
                        false,
                        context,
                        'Supporting',
                        widget.task.documents!
                            // .where((doc) => doc.documentTypeId == 2)
                            .toList(),
                      ),
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  // New method to build the document webview
  Widget _buildDocumentWebView(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with back button and refresh button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  setState(() {
                    showWebView = false;
                    currentDocumentUrl = null;
                    webViewController = null;
                  });
                },
              ),
              Text(
                'Document Viewer',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () {
                  webViewController?.reload();
                },
              ),
            ],
          ),

          // WebView container with fixed height
          Container(
            height: MediaQuery.of(context).size.height *
                0.6, // 60% of screen height
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Stack(
                children: [
                  WebViewWidget(
                    controller: webViewController ?? _initWebViewController(),
                  ),
                  if (isWebViewLoading)
                    const Center(
                      child: CircularProgressIndicator(
                        color: AppColors.primaryBlue,
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // New method to build the form questions view
  Widget _buildFormQuestionsView(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with back button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  setState(() {
                    showFormQuestions = false;
                    currentFormDocument = null;
                    // Keep showDocuments = true to return to document list
                  });
                },
              ),
              // Text(
              //   'Form Questions & Answers',
              //   style: Theme.of(context).textTheme.titleMedium?.copyWith(
              //         fontWeight: FontWeight.w600,
              //       ),
              // ),
              const SizedBox(width: 48), // Placeholder for symmetry
            ],
          ),

          // Form questions and answers content
          Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.6,
            ),
            child: SingleChildScrollView(
              child: _buildFormQuestionsContent(context),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build form questions content
  Widget _buildFormQuestionsContent(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    // Get all forms from the task
    if (widget.task.forms == null || widget.task.forms!.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          'No form questions available for this task.',
          style: textTheme.bodyMedium?.copyWith(
            color: AppColors.black.withOpacity(0.6),
          ),
        ),
      );
    }

    List<Widget> formWidgets = [];

    for (var form in widget.task.forms!) {
      if (form.questions != null && form.questions!.isNotEmpty) {
        formWidgets.add(_buildFormSection(context, form));
        formWidgets.add(const SizedBox(height: 16));
      }
    }

    if (formWidgets.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          'No questions found in the forms.',
          style: textTheme.bodyMedium?.copyWith(
            color: AppColors.black.withOpacity(0.6),
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: formWidgets,
    );
  }

  // Helper method to build a single form section
  Widget _buildFormSection(BuildContext context, entities.Form form) {
    final textTheme = Theme.of(context).textTheme;
    final formId = (form.formId ?? form.hashCode).toInt(); // Use formId or hashCode as unique identifier
    final isExpanded = expandedForms.contains(formId);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Form header - now tappable
        GestureDetector(
          onTap: () {
            setState(() {
              if (isExpanded) {
                expandedForms.remove(formId);
              } else {
                expandedForms.add(formId);
              }
            });
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: isExpanded
                  ? const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    )
                  : BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    form.formName ?? 'Unnamed Form',
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Icon(
                  isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_right,
                  color: AppColors.black,
                  size: 20,
                ),
              ],
            ),
          ),
        ),

        // Questions and answers - only show when expanded
        if (isExpanded) ...[
          ...form.questions!.asMap().entries.map((entry) {
            final index = entry.key;
            final question = entry.value;
            final isLastQuestion = index == form.questions!.length - 1;
            return _buildQuestionAnswerItem(context, form, question, isLastQuestion);
          }),
        ],
      ],
    );
  }

  // Helper method to build a question and answer item
  Widget _buildQuestionAnswerItem(
      BuildContext context, entities.Form form, entities.Question question, [bool isLastQuestion = false]) {
    final textTheme = Theme.of(context).textTheme;

    // Find the corresponding answer for this question
    entities.QuestionAnswer? answer;
    if (form.questionAnswers != null) {
      try {
        answer = form.questionAnswers!.firstWhere(
          (qa) => qa.questionId == question.questionId,
        );
      } catch (e) {
        // No answer found for this question
        answer = null;
      }
    }

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: isLastQuestion
            ? const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              )
            : null,
        // border: Border(
        //   bottom: BorderSide(color: Colors.grey.shade200),
        //   left: BorderSide(color: Colors.grey.shade300),
        //   right: BorderSide(color: Colors.grey.shade300),
        // ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Question
          Text(
            '${question.questionDescription ?? 'No question description'}',
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: AppColors.black,
            ),
          ),
          const SizedBox(height: 8),

          // Answer
          Text(
            '${_getAnswerText(answer)}',
            style: textTheme.bodyMedium?.copyWith(
              color: AppColors.black.withOpacity(0.8),
            ),
          ),

          // Show question brief if available
          if (question.questionBrief?.isNotEmpty == true) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color: AppColors.lightGrey2.withOpacity(0.3),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'Brief: ${question.questionBrief}',
                style: textTheme.bodySmall?.copyWith(
                  color: AppColors.black.withOpacity(0.7),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Helper method to get answer text from QuestionAnswer
  String _getAnswerText(entities.QuestionAnswer? answer) {
    if (answer == null) {
      return 'No answer ';
    }

    if (answer.measurementTextResult?.isNotEmpty == true) {
      return answer.measurementTextResult!;
    }

    if (answer.measurementOptionId != null) {
      return 'Option ${answer.measurementOptionId}';
    }

    if (answer.measurementOptionIds?.isNotEmpty == true) {
      return 'Options: ${answer.measurementOptionIds}';
    }

    return 'Answer provided (no text available)';
  }

  // Helper method to initialize the WebViewController
  WebViewController _initWebViewController() {
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              isWebViewLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              isWebViewLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {
            setState(() {
              isWebViewLoading = false;
              // webViewHasError = true;
            });
          },
        ),
      )
      ..loadRequest(Uri.parse(currentDocumentUrl!));

    return webViewController!;
  }

  Widget _buildDocumentCategory(bool isMainSection, BuildContext context,
      String title, List<entities.Document> documents) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Category title
        Text(
          title,
          style: textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 12),

        // For main section, always show Brief item
        if (isMainSection)
          _buildBriefItem(context)
        else
          // For supporting documents, show documents list or empty message
          documents.isEmpty
              ? Padding(
                  padding: const EdgeInsets.only(left: 8.0, bottom: 8.0),
                  child: Text(
                    'No $title available',
                    style: textTheme.bodySmall?.copyWith(
                      color: AppColors.black.withOpacity(0.6),
                    ),
                  ),
                )
              : Column(
                  children: documents.map((document) {
                    return _buildDocumentItem(context, document);
                  }).toList(),
                ),
      ],
    );
  }

  // Build the static Brief item for the main section
  Widget _buildBriefItem(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: Padding(
          padding: const EdgeInsets.only(left:14.0),
          child: Image.asset(
            AppAssets.supportDocument,
            scale: 4,
            color: Colors.black,
          ),
        ),
        // Container(
        //   width: 40,
        //   height: 40,
        //   decoration: BoxDecoration(
        //     color: Colors.white,
        //     borderRadius: BorderRadius.circular(16),
        // image: const DecorationImage(
        //   image: NetworkImage(
        //     "https://field.storetrack.com.au/Images/icons/sMonthly-brief-icon-trans.gif",
        //   ),
        //   fit: BoxFit.cover,
        // ),
        // ),
        // ),
        title: Text(
          'Brief',
          style: textTheme.montserratTitleExtraSmall.copyWith(
            color: Colors.black,
          ),
        ),
        // subtitle: Text(
        //   'View form questions and answers',
        //   style: textTheme.bodySmall?.copyWith(
        //     color: AppColors.black.withOpacity(0.6),
        //   ),
        // ),
        // trailing: const Icon(
        //   Icons.keyboard_arrow_right,
        //   size: 24,
        //   color: AppColors.black,
        // ),
        onTap: () {
          setState(() {
            currentFormDocument =
                null; // We don't need a specific document for forms
            showFormQuestions = true;
            showWebView = false;
            showDocuments = true; // Ensure documents section is visible
            showAlert = false; // Hide alerts when showing forms
          });
        },
      ),
    );
  }

  Widget _buildDocumentItem(BuildContext context, entities.Document document) {
    print('link------${document.documentIconLink}');
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Document header
          ListTile(
  // Ensure leading is positioned on the left
  leadingAndTrailingTextStyle: textTheme.montserratTitleExtraSmall,
  
  leading: Container(
    width: 40,
    height: 40,
    alignment: Alignment.centerLeft, // Explicitly align to left
    child: document.documentIconLink != null
        ? Image.network(
            document.documentIconLink!,
            width: 40,
            height: 40,
            fit: BoxFit.cover,
          )
        : Image.asset(
            AppAssets.taskReport,
            width: 40,
            height: 40,
            scale: 3,
            color: Colors.black,
          ),
  ),
  
  // Alternative: Use contentPadding to control spacing
  contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
  
  titleAlignment: ListTileTitleAlignment.center,
  title: Text(
    document.documentName ?? 'Unnamed Document',
    style: textTheme.montserratTitleExtraSmall.copyWith(
      color: Colors.black,
    ),
  ),
  
  trailing: document.documentName?.toLowerCase().contains('brief') == true
      ? null // No trailing icons for Brief documents
      : (document.files != null && document.files!.isNotEmpty
          ? Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Download button
                IconButton(
                  icon: const Icon(Icons.download, size: 20),
                  color: AppColors.black,
                  onPressed: () {
                    final firstFile = document.files!.first;
                    if (firstFile.documentFileLink != null &&
                        firstFile.documentFileLink!.isNotEmpty) {
                      _downloadFile(firstFile);
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Document URL not available'),
                        ),
                      );
                    }
                  },
                ),
                // Open button
                IconButton(
                  icon: const Icon(Icons.open_in_new, size: 20),
                  color: AppColors.black,
                  onPressed: () {
                    final firstFile = document.files!.first;
                    if (firstFile.documentFileLink != null &&
                        firstFile.documentFileLink!.isNotEmpty) {
                      context.router.push(WebBrowserRoute(
                          url: firstFile.documentFileLink!));
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Document URL not available'),
                        ),
                      );
                    }
                  },
                ),
              ],
            )
          : null),
  
  onTap: () {
    // Check if this is a "Brief" document and show forms instead
    if (document.documentName?.toLowerCase().contains('brief') == true) {
      setState(() {
        currentFormDocument = document;
        showFormQuestions = true;
        showWebView = false;
        showDocuments = true; // Ensure documents section is visible
        showAlert = false; // Hide alerts when showing forms
      });
    } else {
      // For other documents, navigate to WebBrowserPage if there's a file URL
      if (document.files != null && document.files!.isNotEmpty) {
        final firstFile = document.files!.first;
        if (firstFile.documentFileLink != null &&
            firstFile.documentFileLink!.isNotEmpty) {
          // Navigate to WebBrowserPage with the document URL
          context.router.push(
              WebBrowserRoute(url: firstFile.documentFileLink!));
        } else {
          // If no URL available, show error message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Document URL not available'),
            ),
          );
        }
      } else {
        // If no files, show message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No files available for this document'),
          ),
        );
      }
    }
  },
)

          // Document files are no longer shown inline since we navigate to WebBrowserPage
          // Only Brief documents might show expanded content (forms)
        ],
      ),
    );
  }

  // Helper method to get a user-friendly file name from URL
  String _getFileName(entities.FileElement file) {
    if (file.documentFileLink == null) {
      return file.fileId != null ? 'File ${file.fileId}' : 'Document File';
    }

    final url = file.documentFileLink!;

    // Try to extract filename from URL
    final uri = Uri.parse(url);
    final pathSegments = uri.pathSegments;
    if (pathSegments.isNotEmpty) {
      final lastSegment = pathSegments.last;
      if (lastSegment.isNotEmpty) {
        return lastSegment;
      }
    }

    // If YouTube link, return a friendly name
    if (url.toLowerCase().contains('youtube.com') ||
        url.toLowerCase().contains('youtu.be')) {
      return 'YouTube Video';
    }

    // Fallback to default name
    return file.fileId != null ? 'File ${file.fileId}' : 'Document File';
  }

  // Download file to device with progress indication
  Future<void> _downloadFile(entities.FileElement file) async {
    if (file.documentFileLink == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Document URL not available'),
        ),
      );
      return;
    }

    try {
      // Create a progress value notifier to track download progress
      ValueNotifier<double> progressNotifier = ValueNotifier<double>(0.0);

      // Create a cancellation token for the download
      CancelToken cancelToken = CancelToken();

      // Show loading indicator with progress and cancel button
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Dialog(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ValueListenableBuilder<double>(
                    valueListenable: progressNotifier,
                    builder: (context, progress, _) {
                      return Column(
                        children: [
                          LinearProgressIndicator(
                            value: progress,
                            backgroundColor: Colors.grey[300],
                            valueColor: const AlwaysStoppedAnimation<Color>(
                                AppColors.primaryBlue),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Downloading file... ${(progress * 100).toInt()}%',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  TextButton(
                    onPressed: () {
                      // Cancel the download
                      cancelToken.cancel('Download canceled by user');
                      Navigator.of(context, rootNavigator: true).pop();
                    },
                    child: const Text('Cancel'),
                  ),
                ],
              ),
            ),
          );
        },
      );

      // Get file name
      final fileName = _getFileName(file);

      // Get download directory
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/$fileName';

      // Create Dio instance
      final dio = Dio();

      // Download file with progress updates and cancellation support
      await dio.download(
        file.documentFileLink!,
        filePath,
        cancelToken: cancelToken,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            // Update progress value (between 0.0 and 1.0)
            final progress = received / total;
            progressNotifier.value = progress;
          }
        },
      );

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Close loading dialog
      Navigator.of(context, rootNavigator: true).pop();

      // Show success message with more user-friendly information
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 10),
              Expanded(
                child: Text('File "$fileName" downloaded successfully'),
              ),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: 'View',
            textColor: Colors.white,
            onPressed: () {
              // Here you could add code to open the file if needed
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    } catch (e) {
      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Close loading dialog if it's still open
      Navigator.of(context, rootNavigator: true).pop();

      // Create a more user-friendly error message
      String errorMessage = 'Failed to download file';

      // Add more specific error details based on exception type
      if (e is DioException) {
        if (e.type == DioExceptionType.cancel) {
          // Download was canceled by user, no need to show error
          return;
        }

        switch (e.type) {
          case DioExceptionType.connectionTimeout:
          case DioExceptionType.sendTimeout:
          case DioExceptionType.receiveTimeout:
            errorMessage = 'Connection timed out. Please try again.';
            break;
          case DioExceptionType.connectionError:
            errorMessage = 'No internet connection. Please check your network.';
            break;
          case DioExceptionType.badResponse:
            errorMessage =
                'Server error (${e.response?.statusCode}). Please try again later.';
            break;
          default:
            errorMessage = 'Download failed: ${e.message}';
            break;
        }
      } else if (e is FileSystemException) {
        errorMessage = 'Storage error: Unable to save file.';
      }

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Dismiss',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    }
  }
}
