import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

import '../widgets/empty_state.dart';

@RoutePage()
class NotesPage extends StatefulWidget {
  final TaskDetail task;

  const NotesPage({
    super.key,
    required this.task,
  });

  @override
  State<NotesPage> createState() => _NotesPageState();
}

class _NotesPageState extends State<NotesPage> {
  @override
  Widget build(BuildContext context) {
    final hasNote =
        widget.task.taskNote != null && widget.task.taskNote!.isNotEmpty;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: const CustomAppBar(
        title: 'Task Note',
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // const Gap(8),

            // Note content with improved styling
            Expanded(
              child: hasNote
                  ? _buildNoteContent(context, hasNote)
                  : _buildEmptyState(context),
            ),

            // const Gap(24),
          ],
        ),
      ),
    );
  }

  Widget _buildNoteContent(BuildContext context, bool hasNote) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      height: double.infinity,
      padding: const EdgeInsets.all(20.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: hasNote
              ? AppColors.primaryBlue.withValues(alpha: 0.2)
              : AppColors.blackTint2.withValues(alpha: 0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: hasNote
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Note content
                SelectableText(
                  widget.task.taskNote!,
                  style: textTheme.montserratParagraphSmall.copyWith(
                    height: 1.6,
                    color: AppColors.black,
                    fontSize: 15,
                  ),
                ),
              ],
            )
          : _buildEmptyState(context),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return const Center(
      child: EmptyState(message: 'No Note Available'),
    );
  }
}
